// --- Canvas and Context ---
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
canvas.width = 800;
canvas.height = 600;

// --- Matter.js Aliases ---
const Engine = Matter.Engine;
const World = Matter.World;
const Bodies = Matter.Bodies;
const Body = Matter.Body;
const Events = Matter.Events;
const Composite = Matter.Composite;
const Query = Matter.Query;
const Bounds = Matter.Bounds; // Added for hitbox checking

// --- Game Engine Setup ---
let engine;
let world;

// --- Asset Management ---
const loadedImages = {};
let assetsToLoad = 0;
let assetsLoaded = 0;

const PLAYER_SCALE = 0.5;
const DEBUG_DRAW_HITBOXES = true; // Set to true to see hitboxes

// --- !!!! YOUR ANIMATION DEFINITIONS !!!! ---
const ANIMATION_DEFINITIONS = {
    idle: { /* ... (no changes) ... */
        fileNamePrefix: 'fighter_Idle_', startFrameNum: 1, endFrameNum: 8, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 120, loop: true,
        physicsWidthFactor: 0.25, physicsHeightFactor: 0.45
    },
    walk: { /* ... (no changes) ... */
        fileNamePrefix: 'fighter_walk_', startFrameNum: 9, endFrameNum: 16, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 100, loop: true,
        physicsWidthFactor: 0.25, physicsHeightFactor: 0.45
    },
    run: { /* ... (no changes) ... */
        fileNamePrefix: 'fighter_run_', startFrameNum: 17, endFrameNum: 24, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 80, loop: true,
        physicsWidthFactor: 0.25, physicsHeightFactor: 0.45
    },
    jump: { /* ... (no changes) ... */
        fileNamePrefix: 'fighter_jump_', startFrameNum: 43, endFrameNum: 47, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 80, loop: false,
        physicsWidthFactor: 0.25, physicsHeightFactor: 0.45
    },
    air_attack: {
        fileNamePrefix: 'fighter_air_attack_', startFrameNum: 62, endFrameNum: 63, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 100, loop: false,
        physicsWidthFactor: 0.3, physicsHeightFactor: 0.45,
        hitbox: {
            damage: 12,
            activeFrames: [0, 1], // Active for frame 0 and 1 of this 2-frame animation
            offsetX: 35 * PLAYER_SCALE,
            offsetY: 0 * PLAYER_SCALE,
            width: 70 * PLAYER_SCALE,
            height: 35 * PLAYER_SCALE
        }
    },
    combo1: { // Assuming 'fighter_combo_xxxx.png' is one sequence
        fileNamePrefix: 'fighter_combo_', startFrameNum: 64, endFrameNum: 70, frameNumberDigits: 4, // 7 frames (0-6)
        frameWidth: 512, frameHeight: 512, animationSpeed: 70, loop: false,
        physicsWidthFactor: 0.3, physicsHeightFactor: 0.45,
        hitbox: {
            damage: 20,
            activeFrames: [2, 3, 4], // Active on 3rd, 4th, 5th frame of combo
            offsetX: 45 * PLAYER_SCALE, // How far in front, scaled
            offsetY: -5 * PLAYER_SCALE, // How high/low relative to body center, scaled
            width: 80 * PLAYER_SCALE,
            height: 40 * PLAYER_SCALE
        }
    },
    dash: { /* ... (no changes) ... */
        fileNamePrefix: 'fighter_dash_', startFrameNum: 33, endFrameNum: 38, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 60, loop: false,
        physicsWidthFactor: 0.3, physicsHeightFactor: 0.40
    },
    slide: { /* ... (no changes) ... */
        fileNamePrefix: 'fighter_slide_', startFrameNum: 25, endFrameNum: 32, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 70, loop: false,
        physicsWidthFactor: 0.3, physicsHeightFactor: 0.35
    },
    hit: { /* ... (no changes) ... */
        fileNamePrefix: 'fighter_hit_', startFrameNum: 48, endFrameNum: 51, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 90, loop: false,
        physicsWidthFactor: 0.25, physicsHeightFactor: 0.45
    },
    death: { /* ... (no changes) ... */
        fileNamePrefix: 'fighter_death_', startFrameNum: 52, endFrameNum: 61, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 100, loop: false,
        physicsWidthFactor: 0.25, physicsHeightFactor: 0.45
    },
    climb: { /* ... (no changes) ... */
        fileNamePrefix: 'fighter_climb_', startFrameNum: 39, endFrameNum: 42, frameNumberDigits: 4,
        frameWidth: 512, frameHeight: 512, animationSpeed: 120, loop: true,
        physicsWidthFactor: 0.25, physicsHeightFactor: 0.45
    }
};
const PATH_PREFIX = 'Fighter sprites/';
const FILE_EXTENSION = '.png';

const BACKGROUND_ASSETS = { /* ... (no changes) ... */
    background: ['background1.png'],
    trees: ['tree1.png', 'tree2.png'],
    ground: ['ground.png'],
    buildings: ['building1.png']
};
const backgroundImages = {};

function loadAssets(callback) { /* ... (no changes, assuming it works) ... */
    console.log("Starting asset loading...");
    for (const category in BACKGROUND_ASSETS) {
        for (const filename of BACKGROUND_ASSETS[category]) {
            assetsToLoad++;
            const img = new Image();
            img.onload = () => {
                assetsLoaded++;
                if (assetsLoaded === assetsToLoad) {
                    console.log("All assets loaded successfully!");
                    callback();
                }
            };
            img.onerror = () => {
                console.error(`Failed to load background: extras/${filename}`);
                assetsLoaded++;
                if (assetsLoaded === assetsToLoad) callback();
            };
            img.src = `extras/${filename}`;
            backgroundImages[filename] = img;
        }
    }
    for (const animKey in ANIMATION_DEFINITIONS) {
        const anim = ANIMATION_DEFINITIONS[animKey];
        anim.imageObjects = [];
        anim.frameCount = anim.endFrameNum - anim.startFrameNum + 1;
        for (let i = 0; i < anim.frameCount; i++) {
            assetsToLoad++;
            const frameFileNumber = anim.startFrameNum + i;
            const frameNumberPadded = frameFileNumber.toString().padStart(anim.frameNumberDigits, '0');
            const path = `${PATH_PREFIX}${anim.fileNamePrefix}${frameNumberPadded}${FILE_EXTENSION}`;
            const img = new Image();
            img.onload = () => {
                assetsLoaded++;
                if (assetsLoaded === assetsToLoad) {
                    console.log("All assets loaded successfully!");
                    callback();
                }
            };
            img.onerror = () => {
                console.error(`Failed to load: ${path}`);
                assetsLoaded++;
                if (assetsLoaded === assetsToLoad) callback();
            };
            img.src = path;
            loadedImages[path] = img;
            anim.imageObjects.push(img);
        }
    }
    if (assetsToLoad === 0) {
        console.warn("No assets defined or to load.");
        callback();
    }
}

let player;
let enemy; // New enemy character
let ground;
let backgroundRenderer;


const PLAYER_DENSITY = 0.0005;
const PLAYER_FRICTION = 0.1;
const PLAYER_RESTITUTION = 0.0;
const PLAYER_WALK_FORCE = 0.0025;
const PLAYER_RUN_FORCE = 0.0105;
const PLAYER_JUMP_FORCE = 0.1;


// --- Helper function to draw a rectangle with a diagonally cut bottom-right corner ---
function drawDiagonalCutBarShape(ctx, x, y, width, height, cutAmount) {
    cutAmount = Math.min(cutAmount, width, height);
    ctx.beginPath();
    ctx.moveTo(x, y);
    ctx.lineTo(x + width, y);
    ctx.lineTo(x + width, y + height - cutAmount);
    ctx.lineTo(x + width - cutAmount, y + height);
    ctx.lineTo(x, y + height);
    ctx.closePath();
}

// --- Helper function for mirrored diagonal cut (bottom-left) ---
function drawMirroredDiagonalCutBarShape(ctx, x, y, width, height, cutAmount) {
    cutAmount = Math.min(cutAmount, width, height);
    ctx.beginPath();
    ctx.moveTo(x, y); // Top-left
    ctx.lineTo(x + width, y); // Top-right
    ctx.lineTo(x + width, y + height); // Bottom-right
    ctx.lineTo(x + cutAmount, y + height); // Point on bottom edge, right of where cut meets left
    ctx.lineTo(x, y + height - cutAmount); // Point on left edge, above where cut meets bottom
    ctx.closePath();
}


class Stickman {
    constructor(id, x, y, initialAnimName = 'idle') {
        this.id = id; // 'player' or 'enemy'
        this.currentAnimationName = initialAnimName;
        this.currentAnimation = ANIMATION_DEFINITIONS[this.currentAnimationName];
        if (!this.currentAnimation) {
            console.error(`Initial animation "${initialAnimName}" not defined! Defaulting...`);
            this.currentAnimationName = Object.keys(ANIMATION_DEFINITIONS)[0];
            this.currentAnimation = ANIMATION_DEFINITIONS[this.currentAnimationName];
        }

        this.scaledFrameWidth = this.currentAnimation.frameWidth * PLAYER_SCALE;
        this.scaledFrameHeight = this.currentAnimation.frameHeight * PLAYER_SCALE;

        this.currentFrameIndex = 0;
        this.animationTimer = 0;
        this.facingDirection = (id === 'player') ? 1 : -1; // Enemy faces player initially
        this.isGrounded = false;
        this.isAttacking = false;
        this.isTakingDamage = false;
        this.isDead = false;
        this.isRunning = false;

        this.maxHealth = 100;
        this.currentHealth = this.maxHealth;

        // Hitbox related
        this.activeHitbox = null; // Will store { x, y, width, height, damage } in world coords
        this.hitTargetsThisAttack = new Set(); // Stores IDs of targets hit in current attack sequence

        const bodyWidth = this.scaledFrameWidth * this.currentAnimation.physicsWidthFactor;
        const bodyHeight = this.scaledFrameHeight * this.currentAnimation.physicsHeightFactor;

        this.body = Bodies.rectangle(x, y, bodyWidth, bodyHeight, {
            density: PLAYER_DENSITY,
            friction: PLAYER_FRICTION,
            restitution: PLAYER_RESTITUTION,
            label: id // Use id as label
        });
        Body.setInertia(this.body, Infinity);
        console.log(`${this.id} body created. Size: ${bodyWidth.toFixed(2)}x${bodyHeight.toFixed(2)} at ${x},${y}`);
    }

    setAnimation(animName, forceInterrupt = false) {
        if (!ANIMATION_DEFINITIONS[animName]) {
            console.warn(`Animation "${animName}" not found for ${this.id}.`);
            return;
        }
        if (this.currentAnimationName === animName && !forceInterrupt) return;

        // Clear hit targets if changing from an attack animation or if forced and was attacking
        if (this.isAttacking || (forceInterrupt && ANIMATION_DEFINITIONS[this.currentAnimationName]?.hitbox)) {
            this.hitTargetsThisAttack.clear();
            this.activeHitbox = null;
        }

        if (!forceInterrupt) {
            if (this.isAttacking && !animName.includes('combo') && animName !== 'hit' && animName !== 'death') return;
            if (this.isTakingDamage && animName !== 'death') return;
            if (this.isDead) return;
        }
        if (this.isDead && animName !== 'death') return;


        this.currentAnimationName = animName;
        this.currentAnimation = ANIMATION_DEFINITIONS[this.currentAnimationName];
        this.currentFrameIndex = 0;
        this.animationTimer = 0;

        this.isAttacking = !!this.currentAnimation.hitbox; // Simpler check: if animation has hitbox, it's an attack
        if(this.isAttacking) {
             this.hitTargetsThisAttack.clear(); // Clear targets for new attack
        }
        this.isTakingDamage = this.currentAnimationName === 'hit';

        this.scaledFrameWidth = this.currentAnimation.frameWidth * PLAYER_SCALE;
        this.scaledFrameHeight = this.currentAnimation.frameHeight * PLAYER_SCALE;
    }

    update(deltaTime) {
        if (this.id === 'enemy' && !this.isDead && !this.isTakingDamage && !this.isAttacking) { // Simple enemy logic: face player
            if (player.body.position.x < this.body.position.x) {
                this.facingDirection = -1;
            } else {
                this.facingDirection = 1;
            }
             if (this.isGrounded) this.setAnimation('idle'); // Enemy just stays idle for now
        }


        if (this.isDead) {
            if (this.currentAnimationName !== 'death') this.setAnimation('death', true);
            if (!this.currentAnimation.loop && this.currentFrameIndex < this.currentAnimation.frameCount - 1) {
                this.animationTimer += deltaTime;
                if (this.animationTimer >= this.currentAnimation.animationSpeed) {
                    this.animationTimer = 0;
                    this.currentFrameIndex++;
                }
            }
            this.activeHitbox = null; // No hitboxes when dead
            return;
        }

        // Animation frame progression
        this.animationTimer += deltaTime;
        if (this.animationTimer >= this.currentAnimation.animationSpeed) {
            this.animationTimer = 0;
            if (this.currentFrameIndex < this.currentAnimation.frameCount - 1) {
                this.currentFrameIndex++;
            } else if (this.currentAnimation.loop) {
                this.currentFrameIndex = 0;
            } else { // Non-looping animation finished
                if (this.isAttacking) {
                    this.isAttacking = false;
                    this.activeHitbox = null;
                    this.hitTargetsThisAttack.clear();
                }
                if (this.isTakingDamage) this.isTakingDamage = false;
                this.setAnimation(this.isGrounded ? 'idle' : 'jump', true);
            }
        }

        this.isGrounded = this.checkGrounded();

        // Hitbox activation
        this.activeHitbox = null; // Reset each frame, activate if conditions met
        if (this.isAttacking && this.currentAnimation.hitbox) {
            const hitboxData = this.currentAnimation.hitbox;
            if (hitboxData.activeFrames.includes(this.currentFrameIndex)) {
                const hx = this.body.position.x + (hitboxData.offsetX * this.facingDirection);
                const hy = this.body.position.y + hitboxData.offsetY;
                this.activeHitbox = {
                    min: { x: hx - hitboxData.width / 2, y: hy - hitboxData.height / 2 },
                    max: { x: hx + hitboxData.width / 2, y: hy + hitboxData.height / 2 },
                    damage: hitboxData.damage,
                    sourceId: this.id // To identify who is attacking
                };
            } else { // If not in active frame, but was attacking, ensure hitbox list is cleared
                 if(!this.currentAnimation.loop && this.currentFrameIndex >= this.currentAnimation.frameCount -1) {
                    this.hitTargetsThisAttack.clear();
                 }
            }
        }


        // State logic (player only for now, enemy handled above)
        if (this.id === 'player' && !this.isAttacking && !this.isTakingDamage) {
            if (this.isGrounded) {
                if (Math.abs(this.body.velocity.x) > 0.1) {
                    this.setAnimation(this.isRunning ? 'run' : 'walk');
                } else {
                    this.setAnimation('idle');
                }
            } else {
                if (this.currentAnimationName !== 'jump' && this.currentAnimationName !== 'air_attack') {
                     this.setAnimation('jump');
                }
            }
        }

        if (this.id === 'player' && this.body.velocity.x > 0.1 && !this.isAttacking) this.facingDirection = 1;
        else if (this.id === 'player' && this.body.velocity.x < -0.1 && !this.isAttacking) this.facingDirection = -1;

    }

    draw() {
        const currentFrameImage = this.currentAnimation.imageObjects[this.currentFrameIndex];
        if (!currentFrameImage || !currentFrameImage.complete || currentFrameImage.naturalWidth === 0) {
            ctx.fillStyle = 'rgba(100,100,100,0.5)';
            const phX = this.body.position.x - this.scaledFrameWidth / 2;
            const phY = this.body.position.y - this.scaledFrameHeight / 2;
            ctx.fillRect(phX, phY, this.scaledFrameWidth, this.scaledFrameHeight);
            return;
        }

        const sourceWidth = currentFrameImage.naturalWidth;
        const sourceHeight = currentFrameImage.naturalHeight;
        const pos = this.body.position;
        ctx.save();
        ctx.translate(pos.x, pos.y);
        if (this.facingDirection === -1) ctx.scale(-1, 1);
        ctx.drawImage(
            currentFrameImage,
            0, 0, sourceWidth, sourceHeight,
            -this.scaledFrameWidth / 2, -this.scaledFrameHeight / 2,
            this.scaledFrameWidth, this.scaledFrameHeight
        );
        ctx.restore();

        // Debug Draw Active Hitbox
        if (DEBUG_DRAW_HITBOXES && this.activeHitbox) {
            ctx.fillStyle = 'rgba(255, 0, 0, 0.3)'; // Red, semi-transparent
            ctx.fillRect(
                this.activeHitbox.min.x,
                this.activeHitbox.min.y,
                this.activeHitbox.max.x - this.activeHitbox.min.x,
                this.activeHitbox.max.y - this.activeHitbox.min.y
            );
            ctx.strokeStyle = 'rgba(255,0,0,0.7)';
            ctx.strokeRect(
                this.activeHitbox.min.x,
                this.activeHitbox.min.y,
                this.activeHitbox.max.x - this.activeHitbox.min.x,
                this.activeHitbox.max.y - this.activeHitbox.min.y
            );
        }
    }

    move(direction) {
        if (this.id === 'enemy' || this.isAttacking || this.isDead || this.isTakingDamage) return;
        const moveForce = this.isRunning ? PLAYER_RUN_FORCE : PLAYER_WALK_FORCE;
        Body.applyForce(this.body, this.body.position, { x: moveForce * direction, y: 0 });
    }

    toggleRunning() {
        if (this.isDead || this.id === 'enemy') return;
        this.isRunning = !this.isRunning;
    }

    jump() {
        if (this.id === 'enemy') return;
        if (this.isGrounded && !this.isAttacking && !this.isDead && !this.isTakingDamage) {
            Body.applyForce(this.body, this.body.position, { x: 0, y: -PLAYER_JUMP_FORCE });
            this.setAnimation('jump', true);
            this.isGrounded = false;
        }
    }

    attack(comboPart) {
        if (this.isDead || this.isTakingDamage || this.id === 'enemy') return;

        if (comboPart === 'air_attack' && !this.isGrounded && !this.isAttacking) {
             this.setAnimation('air_attack', true);
        } else if (this.isGrounded && !this.isAttacking) {
            if (ANIMATION_DEFINITIONS[comboPart]) {
                this.setAnimation(comboPart, true);
                // Slow down slightly only if not moving much already
                if(Math.abs(this.body.velocity.x) < PLAYER_WALK_FORCE * 50) { // arbitrary threshold
                   Body.setVelocity(this.body, { x: this.body.velocity.x * 0.1, y: this.body.velocity.y });
                }
            }
        }
    }

    takeDamage(amount = 20, attackerId = null) {
        if (this.isDead || this.currentHealth <= 0 || this.isTakingDamage) return;

        this.currentHealth -= amount;
        console.log(`${this.id} health: ${this.currentHealth}/${this.maxHealth} (took ${amount} from ${attackerId})`);

        if (this.currentHealth <= 0) {
            this.currentHealth = 0;
            this.die();
        } else {
            this.setAnimation('hit', true);
        }
    }

    die() {
        if (this.isDead) return;
        console.log(`${this.id} died!`);
        this.isDead = true;
        this.currentHealth = 0;
        this.setAnimation('death', true);
        // Body.setStatic(this.body, true); // Optional: make them stop reacting to physics
    }

    checkGrounded() {
        const bodyHalfHeight = (this.scaledFrameHeight * this.currentAnimation.physicsHeightFactor) / 2;
        const rayStart = { x: this.body.position.x, y: this.body.position.y + bodyHalfHeight - 1 };
        const rayEnd = { x: this.body.position.x, y: this.body.position.y + bodyHalfHeight + 5 };
        const allBodiesInWorld = Composite.allBodies(world);
        const collisions = Query.ray(allBodiesInWorld, rayStart, rayEnd);
        for (let i = 0; i < collisions.length; i++) {
            if (collisions[i].bodyA !== this.body && collisions[i].bodyB !== this.body) {
                if (collisions[i].bodyA.label === 'ground' || collisions[i].bodyB.label === 'ground') {
                    return true;
                }
            }
        }
        return false;
    }
}

class BackgroundRenderer { /* ... (no changes) ... */
    constructor() {}
    drawBackground() {
        const bgImage = backgroundImages['background1.png'];
        if (bgImage && bgImage.complete) {
            ctx.drawImage(bgImage, 0, 0, canvas.width, canvas.height);
        } else {
            ctx.fillStyle = '#87CEEB';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }
    }
    drawGround() {
        const groundImg = backgroundImages['ground.png'];
        if (groundImg && groundImg.complete) {
            const groundHeight = 70;
            const groundWidth = canvas.width;
            ctx.drawImage(groundImg, 0, canvas.height - groundHeight + 10, groundWidth, groundHeight);
        }
    }
    drawBuildings() {
        const building1Img = backgroundImages['building1.png'];
        if (building1Img && building1Img.complete) {
            const building1Height = 300;
            const building1Width = 300;
            ctx.drawImage(building1Img, 510, canvas.height - building1Height - 50, building1Width, building1Height);
        }
    }
    drawTrees() {
        const tree2Img = backgroundImages['tree2.png'];
        if (tree2Img && tree2Img.complete) {
            const tree2Scale = 0.5;
            const tree2Width = tree2Img.naturalWidth * tree2Scale;
            const tree2Height = tree2Img.naturalHeight * tree2Scale;
            ctx.drawImage(tree2Img, -90, canvas.height - tree2Height - 30, tree2Width, tree2Height);
        }
    }
    render() {
        this.drawBackground();
        this.drawBuildings();
        this.drawTrees();
        this.drawGround();
    }
    renderForeground() {}
}


const keys = {};
document.addEventListener('keydown', (e) => {
    const key = e.key.toLowerCase();
    keys[key] = true;

    if (player) { // Ensure player exists
        if (key === 'j') {
            if(!player.isGrounded) player.attack('air_attack');
            else player.attack('combo1');
        }
        if ((key === 'w' || key === 'arrowup' || key === ' ')) {
            player.jump();
        }
        if (key === 'h') { // Test damage to player
             if (player) player.takeDamage(10, "test_key_H");
             if (enemy) enemy.takeDamage(15, "test_key_H_for_enemy"); // Also damage enemy for testing
        }
        if (key === 'k') { // Test death for player
             if (player) player.die();
            //  if (enemy) enemy.die(); // Can test enemy death too
        }
        if (key === 'shift') {
            player.toggleRunning();
        }
    }
});
document.addEventListener('keyup', (e) => keys[e.key.toLowerCase()] = false);

function handleContinuousInput() {
    if (!player || player.isDead) return; // Only control player
    if (keys['a'] || keys['arrowleft']) {
        player.move(-1);
    } else if (keys['d'] || keys['arrowright']) {
        player.move(1);
    }
}

function checkHitDetection(attacker, defender) {
    if (attacker && defender && attacker.activeHitbox && !defender.isDead && !defender.isTakingDamage) {
        if (!attacker.hitTargetsThisAttack.has(defender.id)) { // Check if defender already hit by this specific attack
            // Attacker's activeHitbox is already in world {min, max} format
            // Defender's physics body also has bounds: defender.body.bounds
            if (Bounds.overlaps(attacker.activeHitbox, defender.body.bounds)) {
                defender.takeDamage(attacker.activeHitbox.damage, attacker.id);
                attacker.hitTargetsThisAttack.add(defender.id); // Mark defender as hit for this attack swing
            }
        }
    }
}


function initGame() {
    engine = Engine.create();
    world = engine.world;
    engine.gravity.y = 1;
    backgroundRenderer = new BackgroundRenderer();

    const initialAnim = ANIMATION_DEFINITIONS.idle;
    const pScaledHeight = initialAnim.frameHeight * PLAYER_SCALE;
    const pBodyHeight = pScaledHeight * initialAnim.physicsHeightFactor;
    const groundYOffset = 50; // Visual ground line
    const bodyBottomY = canvas.height - groundYOffset - (pBodyHeight / 2) -5;


    player = new Stickman(
        'player',
        canvas.width / 4,
        bodyBottomY,
        'idle'
    );

    enemy = new Stickman(
        'enemy',
        canvas.width * 3 / 4,
        bodyBottomY,
        'idle'
    );

    ground = Bodies.rectangle(canvas.width / 2, canvas.height - (groundYOffset/2) , canvas.width + 100, groundYOffset, { isStatic: true, label: 'ground', friction: 0.5 });
    World.add(world, [player.body, enemy.body, ground]); // Add enemy body

    console.log("Game initialized. Player and Enemy created.");
    lastTime = performance.now();
    requestAnimationFrame(gameLoop);
}

let lastTime = 0;
function gameLoop(currentTime) {
    const deltaTime = (currentTime - lastTime) || (1000 / 60);
    lastTime = currentTime;

    handleContinuousInput(); // Player input

    if (player) player.update(deltaTime);
    if (enemy) enemy.update(deltaTime); // Update enemy

    Engine.update(engine, deltaTime);

    // Hit detection
    checkHitDetection(player, enemy);
    // checkHitDetection(enemy, player); // For when enemy can attack

    if (backgroundRenderer) {
        backgroundRenderer.render();
    } else {
        ctx.fillStyle = '#3498db';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
    }

    if (player) player.draw();
    if (enemy) enemy.draw(); // Draw enemy

    if (backgroundRenderer) {
        backgroundRenderer.renderForeground();
    }

    // --- UI Drawing ---
    // Player Health Bar (Left)
    if (player) {
        const barWidth = 220;
        const barHeight = 25;
        const barX = 15;
        const barY = 15;
        const cutAmount = 12;
        const borderWidth = 2;

        ctx.fillStyle = 'rgba(30, 30, 30, 0.85)';
        drawDiagonalCutBarShape(ctx, barX, barY, barWidth, barHeight, cutAmount);
        ctx.fill();

        ctx.save();
        drawDiagonalCutBarShape(ctx, barX, barY, barWidth, barHeight, cutAmount);
        ctx.clip();
        const currentHealthNormalizedP = player.currentHealth / player.maxHealth;
        const currentHealthRectWidthP = barWidth * Math.max(0, currentHealthNormalizedP);
        let gradStartColorP, gradEndColorP;
        if (currentHealthNormalizedP > 0.6) { gradStartColorP = '#50E050'; gradEndColorP = '#20A020'; }
        else if (currentHealthNormalizedP > 0.3) { gradStartColorP = '#FFDD40'; gradEndColorP = '#FFA000'; }
        else { gradStartColorP = '#F05050'; gradEndColorP = '#C02020'; }

        if (currentHealthRectWidthP > 0) {
            const gradientP = ctx.createLinearGradient(barX, barY, barX, barY + barHeight);
            gradientP.addColorStop(0, gradStartColorP);
            gradientP.addColorStop(1, gradEndColorP);
            ctx.fillStyle = gradientP;
            ctx.fillRect(barX, barY, currentHealthRectWidthP, barHeight);
        }
        ctx.restore();

        ctx.strokeStyle = 'rgba(10, 10, 10, 0.9)';
        ctx.lineWidth = borderWidth;
        drawDiagonalCutBarShape(ctx, barX, barY, barWidth, barHeight, cutAmount);
        ctx.stroke();

        ctx.font = 'bold 14px Arial';
        ctx.textAlign = 'center';
        const healthTextP = `${player.currentHealth} / ${player.maxHealth}`;
        ctx.fillStyle = 'rgba(0,0,0,0.7)';
        ctx.fillText(healthTextP, barX + barWidth / 2, barY + barHeight / 2 + 5 + 1);
        ctx.fillStyle = '#FFFFFF';
        ctx.fillText(healthTextP, barX + barWidth / 2, barY + barHeight / 2 + 5);
        ctx.textAlign = 'left';
    }

    // Enemy Health Bar (Right, Mirrored)
    if (enemy) {
        const barWidth = 220;
        const barHeight = 25;
        const barX = canvas.width - barWidth - 15; // Position on the right
        const barY = 15;
        const cutAmount = 12;
        const borderWidth = 2;

        ctx.fillStyle = 'rgba(30, 30, 30, 0.85)';
        drawMirroredDiagonalCutBarShape(ctx, barX, barY, barWidth, barHeight, cutAmount); // Use mirrored shape
        ctx.fill();

        ctx.save();
        drawMirroredDiagonalCutBarShape(ctx, barX, barY, barWidth, barHeight, cutAmount); // Clip with mirrored shape
        ctx.clip();

        const currentHealthNormalizedE = enemy.currentHealth / enemy.maxHealth;
        // For right-to-left fill, we draw the full bar and position the fill at the end
        const currentHealthRectWidthE = barWidth * Math.max(0, currentHealthNormalizedE);
        const fillX = barX + barWidth - currentHealthRectWidthE; // Calculate starting X for fill

        let gradStartColorE, gradEndColorE;
        if (currentHealthNormalizedE > 0.6) { gradStartColorE = '#50E050'; gradEndColorE = '#20A020'; }
        else if (currentHealthNormalizedE > 0.3) { gradStartColorE = '#FFDD40'; gradEndColorE = '#FFA000'; }
        else { gradStartColorE = '#F05050'; gradEndColorE = '#C02020'; }

        if (currentHealthRectWidthE > 0) {
            const gradientE = ctx.createLinearGradient(barX, barY, barX, barY + barHeight); // Same gradient visually
            gradientE.addColorStop(0, gradStartColorE);
            gradientE.addColorStop(1, gradEndColorE);
            ctx.fillStyle = gradientE;
            ctx.fillRect(fillX, barY, currentHealthRectWidthE, barHeight); // Draw from calculated fillX
        }
        ctx.restore();

        ctx.strokeStyle = 'rgba(10, 10, 10, 0.9)';
        ctx.lineWidth = borderWidth;
        drawMirroredDiagonalCutBarShape(ctx, barX, barY, barWidth, barHeight, cutAmount);
        ctx.stroke();

        ctx.font = 'bold 14px Arial';
        ctx.textAlign = 'center';
        const healthTextE = `${enemy.currentHealth} / ${enemy.maxHealth}`;
        ctx.fillStyle = 'rgba(0,0,0,0.7)';
        ctx.fillText(healthTextE, barX + barWidth / 2, barY + barHeight / 2 + 5 + 1);
        ctx.fillStyle = '#FFFFFF';
        ctx.fillText(healthTextE, barX + barWidth / 2, barY + barHeight / 2 + 5);
        ctx.textAlign = 'left';
    }


    // Controls Text
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '12px Arial';
    const controlsTextY = player ? 50 : 20; // Adjust if player bar is not shown
    ctx.fillText('Controls: A/D-Move, Space/W-Jump, J-Attack, H-TestDmg, K-TestKill, Shift-Run', 10, controlsTextY);


    requestAnimationFrame(gameLoop);
}

console.log("Requesting asset load...");
loadAssets(initGame);